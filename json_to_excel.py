import json
import pandas as pd
import re

def parse_table_data(json_data):
    """
    解析JSON数据，提取表格信息
    """
    # 创建一个字典来存储表格数据
    table_data = {}
    
    # 遍历JSON数据，按行列位置组织数据
    for cell in json_data:
        row = cell['start_row']
        col = cell['start_col']
        text = cell['text'].strip()
        
        if row not in table_data:
            table_data[row] = {}
        
        table_data[row][col] = text
    
    return table_data

def create_dataframe(table_data):
    """
    将表格数据转换为DataFrame
    """
    # 获取最大行数和列数
    max_row = max(table_data.keys()) if table_data else 0
    max_col = 0
    for row_data in table_data.values():
        if row_data:
            max_col = max(max_col, max(row_data.keys()))
    
    # 创建数据列表
    rows = []
    for row_idx in range(max_row + 1):
        row_data = []
        for col_idx in range(max_col + 1):
            if row_idx in table_data and col_idx in table_data[row_idx]:
                row_data.append(table_data[row_idx][col_idx])
            else:
                row_data.append('')
        rows.append(row_data)
    
    # 创建DataFrame
    if rows:
        # 使用第一行作为列名
        if len(rows) > 1:
            df = pd.DataFrame(rows[1:], columns=rows[0])
        else:
            df = pd.DataFrame(rows)
    else:
        df = pd.DataFrame()
    
    return df

def clean_text(text):
    """
    清理文本，处理换行符等
    """
    # 将换行符替换为空格，但保持基本格式
    text = re.sub(r'\n+', ' ', text)
    # 去除多余空格
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def clean_column_names(df):
    """
    清理列名，去除换行符
    """
    new_columns = []
    for col in df.columns:
        # 清理列名中的换行符和多余空格
        clean_col = re.sub(r'\n+', ' ', str(col))
        clean_col = re.sub(r'\s+', ' ', clean_col).strip()
        new_columns.append(clean_col)

    df.columns = new_columns
    return df

def main():
    # 读取JSON文件
    try:
        with open('1111.json', 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        print(f"成功读取JSON文件，包含 {len(json_data)} 个单元格")
        
        # 解析表格数据
        table_data = parse_table_data(json_data)
        
        # 创建DataFrame
        df = create_dataframe(table_data)

        # 清理列名
        df = clean_column_names(df)

        # 清理文本数据
        for col in df.columns:
            if df[col].dtype == 'object':
                df[col] = df[col].apply(lambda x: clean_text(str(x)) if pd.notna(x) else '')
        
        # 保存到Excel文件
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f'amazon_table_{timestamp}.xlsx'
        df.to_excel(output_file, index=False, engine='openpyxl')
        
        print(f"数据已成功保存到 {output_file}")
        print(f"表格大小: {df.shape[0]} 行 x {df.shape[1]} 列")
        
        # 显示前几行数据预览
        print("\n数据预览:")
        print(df.head())
        
        return df
        
    except FileNotFoundError:
        print("错误: 找不到文件 '1111.json'")
        return None
    except json.JSONDecodeError:
        print("错误: JSON文件格式不正确")
        return None
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return None

if __name__ == "__main__":
    df = main()
