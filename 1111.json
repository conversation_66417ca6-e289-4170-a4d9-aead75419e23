[{"end_col": 0, "text": "SKU", "borders": {"top": 1, "bottom": 1, "left": 1, "right": 0}, "position": [103, 283, 327, 283, 327, 365, 103, 365], "lines": [{"score": 0.999, "type": "text", "position": [121, 327, 183, 327, 183, 358, 121, 358], "angle": 0, "direction": 1, "handwritten": 0, "text": "SKU"}], "start_row": 0, "start_col": 0, "end_row": 0}, {"borders": {"top": 1, "bottom": 1, "left": 0, "right": 0}, "position": [327, 283, 529, 283, 529, 365, 327, 365], "lines": [{"angle": 0, "direction": 1, "handwritten": 0, "text": "ASIN", "score": 0.999, "type": "text", "position": [333, 327, 405, 327, 405, 358, 333, 358]}], "start_row": 0, "start_col": 1, "end_row": 0, "end_col": 1, "text": "ASIN"}, {"start_row": 0, "start_col": 2, "end_row": 0, "end_col": 2, "text": "Product Name", "borders": {"top": 1, "bottom": 1, "left": 0, "right": 0}, "position": [529, 283, 1383, 283, 1383, 365, 529, 365], "lines": [{"position": [544, 330, 732, 330, 732, 358, 544, 358], "angle": 0, "direction": 1, "handwritten": 0, "text": "Product Name", "score": 0.989, "type": "text"}]}, {"end_row": 0, "end_col": 3, "text": "Qty", "borders": {"top": 1, "bottom": 1, "left": 0, "right": 0}, "position": [1383, 283, 1530, 283, 1530, 365, 1383, 365], "lines": [{"score": 0.999, "type": "text", "position": [1446, 330, 1495, 330, 1495, 361, 1446, 361], "angle": 0, "direction": 1, "handwritten": 0, "text": "Qty"}], "start_row": 0, "start_col": 3}, {"lines": [{"text": "Unit", "score": 0.999, "type": "text", "position": [1578, 296, 1635, 296, 1635, 325, 1578, 325], "angle": 0, "direction": 1, "handwritten": 0}, {"position": [1565, 330, 1635, 330, 1635, 358, 1565, 358], "angle": 0, "direction": 1, "handwritten": 0, "text": "Price", "score": 0.999, "type": "text"}], "start_row": 0, "start_col": 4, "end_row": 0, "end_col": 4, "text": "Unit\nPrice", "borders": {"top": 1, "bottom": 1, "left": 0, "right": 0}, "position": [1530, 283, 1691, 283, 1691, 365, 1530, 365]}, {"position": [1691, 283, 1915, 283, 1915, 365, 1691, 365], "lines": [{"angle": 0, "direction": 1, "handwritten": 0, "text": "Item <PERSON>", "score": 0.989, "type": "text", "position": [1740, 296, 1918, 296, 1918, 325, 1740, 325]}, {"text": "Before Tax", "score": 0.995, "type": "text", "position": [1771, 327, 1919, 330, 1918, 360, 1771, 357], "angle": 0, "direction": 1, "handwritten": 0}], "start_row": 0, "start_col": 5, "end_row": 0, "end_col": 5, "text": "Item Subtotal\nBefore Tax", "borders": {"top": 1, "bottom": 1, "left": 0, "right": 1}}, {"text": "2LW1vW422098 ", "borders": {"top": 1, "bottom": 0, "left": 1, "right": 0}, "position": [103, 365, 327, 365, 327, 507, 103, 507], "lines": [{"text": "2LW1vW422098 ", "score": 0.977, "type": "text", "position": [126, 380, 334, 380, 334, 402, 126, 402], "angle": 0, "direction": 1, "handwritten": 0}], "start_row": 1, "start_col": 0, "end_row": 1, "end_col": 0}, {"start_col": 1, "end_row": 1, "end_col": 1, "text": "BOFBRTN5JT", "borders": {"top": 1, "bottom": 0, "left": 0, "right": 0}, "position": [327, 365, 529, 365, 529, 507, 327, 507], "lines": [{"handwritten": 0, "text": "BOFBRTN5JT", "score": 0.977, "type": "text", "position": [334, 380, 502, 380, 502, 402, 334, 402], "angle": 0, "direction": 1}], "start_row": 1}, {"position": [529, 365, 1383, 365, 1383, 507, 529, 507], "lines": [{"score": 0.986, "type": "text", "position": [544, 379, 1284, 379, 1284, 410, 544, 410], "angle": 0, "direction": 1, "handwritten": 0, "text": "First Ride On 12V Ride on Toys for Kids, Licensed Ford F-150"}, {"type": "text", "position": [544, 410, 1279, 410, 1279, 441, 544, 441], "angle": 0, "direction": 1, "handwritten": 0, "text": "Raptor Kids Ride on Car with Parent Remote Control,<PERSON><PERSON>", "score": 0.993}, {"angle": 0, "direction": 1, "handwritten": 0, "text": "Electric Car with Radio, Bluetooth, Truck Bed Storage, Soft Start", "score": 0.987, "type": "text", "position": [544, 441, 1310, 441, 1310, 472, 544, 472]}, {"score": 0.992, "type": "text", "position": [547, 472, 1263, 472, 1263, 503, 547, 503], "angle": 0, "direction": 1, "handwritten": 0, "text": "(Blue) [Evergreen promotions 1k applied, new price @$0.00]"}], "start_row": 1, "start_col": 2, "end_row": 1, "end_col": 2, "text": "First Ride On 12V Ride on Toys for Kids, Licensed Ford F-150\nRaptor Kids Ride on Car with Parent Remote Control,Toddler\nElectric Car with Radio, Bluetooth, Truck Bed Storage, Soft Start\n(Blue) [Evergreen promotions 1k applied, new price @$0.00]", "borders": {"top": 1, "bottom": 0, "left": 0, "right": 0}}, {"text": "10", "borders": {"top": 1, "bottom": 0, "left": 0, "right": 0}, "position": [1383, 365, 1530, 365, 1530, 507, 1383, 507], "lines": [{"position": [1462, 379, 1495, 379, 1495, 407, 1462, 407], "angle": 0, "direction": 1, "handwritten": 0, "text": "10", "score": 0.999, "type": "text"}], "start_row": 1, "start_col": 3, "end_row": 1, "end_col": 3}, {"start_col": 4, "end_row": 1, "end_col": 4, "text": "$0.00", "borders": {"top": 1, "bottom": 0, "left": 0, "right": 0}, "position": [1530, 365, 1691, 365, 1691, 507, 1530, 507], "lines": [{"text": "$0.00", "score": 0.999, "type": "text", "position": [1562, 379, 1637, 379, 1637, 407, 1562, 407], "angle": 0, "direction": 1, "handwritten": 0}], "start_row": 1}, {"text": "$0.00", "borders": {"right": 1, "top": 1, "bottom": 0, "left": 0}, "position": [1691, 365, 1915, 365, 1915, 507, 1691, 507], "lines": [{"type": "text", "position": [1843, 379, 1918, 379, 1918, 407, 1843, 407], "angle": 0, "direction": 1, "handwritten": 0, "text": "$0.00", "score": 0.999}], "start_row": 1, "start_col": 5, "end_row": 1, "end_col": 5}]