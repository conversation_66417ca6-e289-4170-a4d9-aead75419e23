import json
import pandas as pd
import re
import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows

def parse_table_data(json_data):
    """
    解析JSON数据，提取表格信息
    """
    # 创建一个字典来存储表格数据
    table_data = {}
    
    # 遍历JSON数据，按行列位置组织数据
    for cell in json_data:
        row = cell['start_row']
        col = cell['start_col']
        text = cell['text'].strip()
        
        if row not in table_data:
            table_data[row] = {}
        
        table_data[row][col] = text
    
    return table_data

def create_dataframe(table_data):
    """
    将表格数据转换为DataFrame
    """
    # 获取最大行数和列数
    max_row = max(table_data.keys()) if table_data else 0
    max_col = 0
    for row_data in table_data.values():
        if row_data:
            max_col = max(max_col, max(row_data.keys()))
    
    # 创建数据列表
    rows = []
    for row_idx in range(max_row + 1):
        row_data = []
        for col_idx in range(max_col + 1):
            if row_idx in table_data and col_idx in table_data[row_idx]:
                row_data.append(table_data[row_idx][col_idx])
            else:
                row_data.append('')
        rows.append(row_data)
    
    # 创建DataFrame
    if rows:
        # 使用第一行作为列名
        if len(rows) > 1:
            df = pd.DataFrame(rows[1:], columns=rows[0])
        else:
            df = pd.DataFrame(rows)
    else:
        df = pd.DataFrame()
    
    return df

def clean_text(text):
    """
    清理文本，处理换行符等
    """
    if pd.isna(text) or text == '':
        return ''
    
    # 将换行符替换为空格，但保持基本格式
    text = str(text)
    text = re.sub(r'\n+', ' ', text)
    # 去除多余空格
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def clean_column_names(df):
    """
    清理列名，去除换行符
    """
    new_columns = []
    for col in df.columns:
        # 清理列名中的换行符和多余空格
        clean_col = re.sub(r'\n+', ' ', str(col))
        clean_col = re.sub(r'\s+', ' ', clean_col).strip()
        new_columns.append(clean_col)
    
    df.columns = new_columns
    return df

def format_excel(df, filename):
    """
    格式化Excel文件
    """
    wb = Workbook()
    ws = wb.active
    ws.title = "Amazon Table Data"
    
    # 添加数据到工作表
    for r in dataframe_to_rows(df, index=False, header=True):
        ws.append(r)
    
    # 设置标题行样式
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
    
    # 设置边框
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # 应用标题行样式
    for cell in ws[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = thin_border
    
    # 设置数据行样式
    for row in ws.iter_rows(min_row=2, max_row=ws.max_row):
        for cell in row:
            cell.border = thin_border
            cell.alignment = Alignment(vertical="center", wrap_text=True)
    
    # 自动调整列宽
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        
        # 设置最小和最大宽度
        adjusted_width = min(max(max_length + 2, 10), 50)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # 保存文件
    wb.save(filename)

def main():
    # 读取JSON文件
    try:
        with open('1111.json', 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        print(f"成功读取JSON文件，包含 {len(json_data)} 个单元格")
        
        # 解析表格数据
        table_data = parse_table_data(json_data)
        
        # 创建DataFrame
        df = create_dataframe(table_data)
        
        # 清理列名
        df = clean_column_names(df)
        
        # 清理文本数据
        for col in df.columns:
            df[col] = df[col].apply(clean_text)
        
        # 生成带时间戳的文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f'amazon_table_formatted_{timestamp}.xlsx'
        
        # 保存格式化的Excel文件
        format_excel(df, output_file)
        
        print(f"数据已成功保存到 {output_file}")
        print(f"表格大小: {df.shape[0]} 行 x {df.shape[1]} 列")
        
        # 显示数据预览
        print("\n数据预览:")
        print(df.to_string(max_colwidth=50))
        
        # 显示详细的单元格内容
        print("\n详细数据:")
        for idx, row in df.iterrows():
            print(f"\n第 {idx + 1} 行数据:")
            for col in df.columns:
                print(f"  {col}: {row[col]}")
        
        return df
        
    except FileNotFoundError:
        print("错误: 找不到文件 '1111.json'")
        return None
    except json.JSONDecodeError:
        print("错误: JSON文件格式不正确")
        return None
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return None

if __name__ == "__main__":
    df = main()
