import json
import pandas as pd
import re

def extract_table_from_json(json_file_path):
    """
    从JSON文件中提取表格数据并保存为Excel
    """
    try:
        # 读取JSON文件
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"读取到 {len(data)} 个单元格数据")
        
        # 按行列组织数据
        table = {}
        for cell in data:
            row = cell['start_row']
            col = cell['start_col']
            text = cell['text'].strip()
            
            if row not in table:
                table[row] = {}
            table[row][col] = text
        
        # 转换为列表格式
        rows = []
        max_row = max(table.keys()) if table else 0
        max_col = max(max(row_data.keys()) for row_data in table.values() if row_data) if table else 0
        
        for row_idx in range(max_row + 1):
            row_data = []
            for col_idx in range(max_col + 1):
                if row_idx in table and col_idx in table[row_idx]:
                    # 清理文本：去除多余的换行符和空格
                    text = table[row_idx][col_idx]
                    text = re.sub(r'\n+', ' ', text)  # 换行符替换为空格
                    text = re.sub(r'\s+', ' ', text)  # 多个空格替换为单个空格
                    row_data.append(text.strip())
                else:
                    row_data.append('')
            rows.append(row_data)
        
        # 创建DataFrame
        if len(rows) > 1:
            # 第一行作为列名
            df = pd.DataFrame(rows[1:], columns=rows[0])
        else:
            df = pd.DataFrame(rows)
        
        # 清理列名
        df.columns = [re.sub(r'\n+', ' ', str(col)).strip() for col in df.columns]
        
        # 保存为Excel
        output_file = 'amazon_table_data.xlsx'
        df.to_excel(output_file, index=False, engine='openpyxl')
        
        print(f"数据已保存到: {output_file}")
        print(f"表格尺寸: {df.shape[0]} 行 x {df.shape[1]} 列")
        
        # 显示数据
        print("\n提取的表格数据:")
        print("=" * 80)
        for i, (index, row) in enumerate(df.iterrows()):
            print(f"第 {i+1} 行:")
            for col in df.columns:
                print(f"  {col}: {row[col]}")
            print("-" * 40)
        
        return df
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        return None

# 主程序
if __name__ == "__main__":
    # 处理JSON文件
    df = extract_table_from_json('1111.json')
    
    if df is not None:
        print("\n处理完成！")
        print("您可以在当前目录找到生成的Excel文件：amazon_table_data.xlsx")
    else:
        print("处理失败，请检查JSON文件格式。")
